// styling
import styled from "styled-components/macro";
import { dark, light, flex, colors, textSizes, breakpoints } from "@styles/vars";

// styled components
import { Footer } from "@components/Messenger/style";
import ShapeButton from "@ui/ShapeButton";

// hooks
import { useRef } from "react";
import { useDispatch, useSelector } from "react-redux";

// utils
import PropTypes from "prop-types";
import moment from "moment";
import { nanoid } from "nanoid";
import { db } from "../../../config/firebase.config";

// actions
import { addMessage } from "@store/slices/messenger";
import { addDoc, collection, doc, increment, setDoc, Timestamp, updateDoc } from "firebase/firestore";
import { COLLECTIONS } from "@constants/app";
import { updateChatAction } from "@store/slices/chats";
import Btn from "@ui/Btn";
import theme from "styled-theming";
import { Box, IconButton, Popover } from "@mui/material";
import { useState } from "react";
import { Close } from "@mui/icons-material";
import { convertToDataURL } from "@utils/helpers";
import { uploadImageToFirebase } from "@utils/storage";

const bg = theme("theme", {
  light: light.bodyBg,
  dark: dark.highlight,
});

const InputFooter = styled(Footer)`
  padding: 15px 24px 20px;
  form {
    display: flex;
    ${flex.between};
    gap: 8px;

    input {
      height: 40px;
      max-width: calc(100% - 98px);
      flex-grow: 1;

      &::placeholder {
        font-size: ${textSizes["14"]};
      }
    }

    ${breakpoints.tablet} {
      input::placeholder {
        font-size: ${textSizes["18"]};
      }
    }
  }
`;

const IconBtn = styled.button`
  width: 40px;
  aspect-ratio: 1;
  background-color: ${bg};
  color: ${colors.gray};
  ${flex.col}
  ${flex.center}
  position: relative;
  transition: color var(--transition), background-color var(--transition);
  border-radius: 999px;

  &:hover,
  &:focus {
    background-color: ${colors.blue};
    color: #fff;
  }
  &:disabled {
    cursor: not-allowed;
    background-color: ${bg};
    color: ${colors.gray};
    opacity: 0.7;
  }
  &:disabled &:hover {
    cursor: not-allowed;
    background-color: ${bg};
    color: ${colors.gray};
  }
`;

const PreviewImage = styled.img`
  height: 40px;
  width: 40px;
  border-radius: 8px;
`;

const HiddenInput = styled.input`
  display: none;
`;

const Input = ({ id, elRef, activeChat, currentChat, otherUserId }) => {
  const inputRef = useRef(null);
  const fileInputRef = useRef(null);
  const imagePickerBtnRef = useRef(null);

  const [isLoading, setLoading] = useState(false);
  const [image, setImage] = useState(null);

  const { user } = useSelector((state) => state.auth);
  const dispatch = useDispatch();

const sendNotification = async ({
  title,
  body,
  userId,
  recipientId,
  chatId,
}: NotificationProps) => {
  try {
    const payload = {
      title: capitalizeFirstLetter(title),
      body,
      userId,
      recipientId,
      chatId,
      read: true,
      createdAt: firestore.FieldValue.serverTimestamp(), // More reliable than new Date()
      // receiverIds: [userId],
      screen: 'ChatDetails',
    };

    await firestore().collection('Notifications').add(payload);
  } catch (error) {
    console.log('error while sending notification', error);
  }
};

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!inputRef?.current?.value?.trim() && !image) return;
    setLoading(true);
    const messageId = nanoid();
    const messagesRef = doc(collection(db, COLLECTIONS.CHATS, activeChat, "Messages"), messageId);

    const message = {
      createdAt: Timestamp.fromDate(new Date()),
      read: false,
      recipient: otherUserId,
      participants: [otherUserId, user?.id],
      sender: user?.id,
      text: inputRef.current.value,
      id: messageId,
      _id: messageId,
      user: {
        _id: user?.id,
      },
    };
    console.log("message >>", message);

    if (image) {
      const res = await uploadImageToFirebase("Chats", image);
      message["image"] = res;
    }
    setImage(null);

    inputRef.current.value = "";
    // inputRef.current.blur();

    await setDoc(messagesRef, message)
      .then(() => {
        console.log("message sent");
      })
      .catch((error) => console.log("Send Message > ", error));
    setLoading(false);

    const payload_update_chat = {
      lastMessage: {
        text: message.text,  // Only pass the text string, not the entire object
        senderId: user?.id,
        read: false,
        createdAt: Timestamp.fromDate(new Date()),
        // Add image info if present
        ...(message.image && { image: message.image })
      },
      updatedAt: Timestamp.fromDate(new Date()),
      [`unreadCounts.${otherUserId}`]: increment(1),
      [`unreadCounts.${user?.id}`]: 0,
    };
    await updateDoc(doc(db, COLLECTIONS.CHATS, activeChat), payload_update_chat);
  };

  function onClickImage(e) {
    if (fileInputRef.current) {
      fileInputRef?.current?.click();
    }
  }

  async function onChangeImage(e) {
    const file = e?.target?.files ? e?.target?.files[0] : null;
    if (file) {
      const data_url = await convertToDataURL(file);
      setImage(data_url);
    }
  }

  return (
    <>
      <InputFooter ref={elRef}>
        {image ? (
          <Box sx={{ width: "100%", backgroundColor: "#eee", borderRadius: 2, mb: "6px", p: "6px" }}>
            <Box sx={{ position: "relative", width: "fit-content" }}>
              <PreviewImage src={image} />
              <IconButton
                sx={{
                  height: "10px",
                  width: "10px",
                  position: "absolute",
                  top: "0",
                  right: "0",
                  zIndex: 9,
                  backgroundColor: "#ccc",
                  ":hover": { backgroundColor: "#bbb" },
                }}
                onClick={() => setImage(null)}
              >
                <Close sx={{ height: "12px", width: "12px" }} />
              </IconButton>
            </Box>
          </Box>
        ) : null}
        <form onSubmit={handleSubmit}>
          <input ref={inputRef} type="text" placeholder="Type a message..." spellCheck={true} />
          {/* <ShapeButton shape="round" icon="paper-plane" label="Send" type="submit" /> */}
          {/* <ShapeButton shape="round" icon="paper-plane" label="Send" type="submit" /> */}
          <IconBtn shape="round" icon="paper-plane" label="Send" type="submit" disabled={isLoading}>
            <i className={`icon icon-paper-plane`} />
          </IconBtn>
          <IconBtn
            shape="round"
            icon="image"
            label="Image"
            type="button"
            onClick={(e) => onClickImage(e)}
            ref={imagePickerBtnRef}
            disabled={isLoading}
          >
            <i className={`icon icon-image`} />
          </IconBtn>
        </form>
        <HiddenInput type="file" accept="image/*" ref={fileInputRef} onChange={onChangeImage} />
      </InputFooter>

      {/* <Popover
        anchorOrigin={{
          vertical: 50,
          horizontal: -330,
        }}
        transformOrigin={{
          vertical: "bottom",
          horizontal: "center",
        }}
        open={isPopoverOpen}
        anchorEl={imagePickerBtnRef?.current}
      >
        <Box sx={{ width: "100%", backgroundColor: bg, minWidth: "750px", minHeight: "350px" }}>
          The content of the Popover.
        </Box>
      </Popover> */}
    </>
  );
};

Input.propTypes = {
  db: PropTypes.oneOf(["doctor", "patient"]).isRequired,
  id: PropTypes.string.isRequired,
};

export default Input;
